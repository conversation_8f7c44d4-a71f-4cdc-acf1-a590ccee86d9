local char = require("character")
local utils = require("init.utils")
local configMod = require("config.config")

local userConfig = configMod.userConfig or {}
local uiConfig = configMod.uiConfig or {}
local packageName = (configMod.开发设置 and configMod.开发设置.应用包名) or ""

local Automation = {}
Automation.__index = Automation

-- 每日任务容错窗口（秒），例如 10 分钟
local DAILY_WINDOW_SECONDS = 10 * 60

-- 小工具：获取当天字符串，用于每日重置
local function todayKey()
    return os.date("%Y-%m-%d")
end

-- 调用某类“定时内容”后，需要重置进图状态
local function runTimed(taskName, fn, ...)
    print("[Timed] 开始执行定时任务: " .. taskName)
    local ok, ret = pcall(fn, char, ...)
    if not ok then
        print("[Timed] 任务异常: " .. tostring(ret))
        ret = false
    end
    print("[Timed] 结束: " .. taskName .. ", 返回=" .. tostring(ret))
    return ret
end

function Automation:new()
    local o = {
        inMap = false,
        lastMapEnter = 0,

        -- 重启相关
        lastRestartAt = os.time(),

        -- 周期任务
        nextForbiddenAt = 0,      -- 禁地，每6小时
        nextWorldBossAt = 0,      -- 世界首领，每15分钟
        worldBossFailCount = 0,   -- 当天失败计数
        worldBossDayKey = todayKey(),

        -- 每日定时任务（时间为当天 HH:MM 触发一次）
        dailyMarks = {},          -- 记录当天是否已执行

        -- 启动一次性任务（到次日不再执行）
        startupDayKey = nil,
        startupDone = false,

        -- 喊话调度
        shoutPlan = {},           -- { [key] = {interval=sec, content=..., enabled=true, nextAt=os.time()} }

        -- 日切换检查
        dayKey = todayKey(),
    }
    setmetatable(o, Automation)
    o:buildShoutPlan()
    o:initSchedules()
    return o
end

function Automation:buildShoutPlan()
    self.shoutPlan = {}
    local shout = (userConfig and userConfig.喊话设置) or {}
    for k, v in pairs(shout) do
        if type(v) == "table" then
            local enabled = v.enabled ~= false
            local content = v.content or ""
            local interval = tonumber(v.interval or 0) or 0
            if enabled and interval > 0 and content ~= "" then
                self.shoutPlan[k] = {
                    enabled = true,
                    content = content,
                    interval = interval,
                    nextAt = os.time() + interval,
                }
            end
        end
    end
end

local function todayAt(hour, min)
    local now = os.time()
    local t = os.date("*t", now)
    t.hour = hour; t.min = min; t.sec = 0
    return os.time(t)
end

function Automation:initSchedules()
    local now = os.time()
    -- 禁地：每6小时
    self.nextForbiddenAt = now
    -- 世界首领：每15分钟
    self.nextWorldBossAt = now

    -- 每日定时点（带 userConfig 开关控制）
    self.dailySchedule = {
        ["22:00_福利宝箱"] = {
            time = todayAt(22, 0), ran = false,
            enabled = (userConfig.升级宝箱 and userConfig.升级宝箱.enable)
                      or (userConfig.散人福利 and userConfig.散人福利.enable),
            run = function()
                local ok = false
                if userConfig.升级宝箱 and userConfig.升级宝箱.enable then
                    ok = runTimed("领取宝箱", char.领取宝箱) or ok
                end
                if userConfig.散人福利 and userConfig.散人福利.enable then
                    ok = runTimed("散人福利", char.领取散人福利) or ok
                end
                return ok
            end
        },
        ["20:00_星空秘境"] = {
            time = todayAt(20, 0), ran = false,
            enabled = (userConfig.星空秘境 and userConfig.星空秘境.enable) or false,
            run = function()
                return runTimed("星空秘境", char.星空秘境)
            end
        },
        ["13:00_武林争霸"] = {
            time = todayAt(13, 0), ran = false,
            enabled = (userConfig.武林争霸 and userConfig.武林争霸.enable) or false,
            run = function()
                return runTimed("武林争霸", char.武林争霸)
            end
        },
        ["21:00_武林争霸"] = {
            time = todayAt(21, 0), ran = false,
            enabled = (userConfig.武林争霸 and userConfig.武林争霸.enable) or false,
            run = function()
                return runTimed("武林争霸", char.武林争霸)
            end
        },
    }

    self.startupDayKey = todayKey()
    self.startupDone = false
end

function Automation:checkDayRollover()
    local k = todayKey()
    if k ~= self.dayKey then
        print("[Daily] 跨天重置：" .. self.dayKey .. " -> " .. k)
        self.dayKey = k
        -- 重置世界首领失败计数
        self.worldBossFailCount = 0
        self.worldBossDayKey = k
        -- 重置每日定时任务时间戳
        for _, item in pairs(self.dailySchedule or {}) do
            item.time = todayAt(os.date("*t").hour, os.date("*t").min) -- 将会立刻在下面刷新到当天具体时间
        end
        -- 重新初始化每日任务时间
        self:initSchedules()
        -- 启动一次性任务标记
        self.startupDayKey = k
        self.startupDone = false
        -- 喊话计划 nextAt 重置为当前+interval
        self:buildShoutPlan()
    end
end

function Automation:ensureInMap()
    if self.inMap then return true end
    if userConfig.map_enabled and userConfig.map_enabled.enable and userConfig.map then
        local ok = char:进入打宝地图(userConfig.map)
        if ok then
            self.inMap = true
            self.lastMapEnter = os.time()
            char:开启目标选择怪物界面()
            char:开启自动战斗功能()
            print("[Map] 已进入地图并开启自动战斗: " .. tostring(userConfig.map))
            return true
        else
            print("[Map] 进入地图失败: " .. tostring(userConfig.map))
            return false
        end
    else
        -- 未开启自动进图，也视为可挂机的场景
        self.inMap = true
        char:开启目标选择怪物界面()
        char:开启自动战斗功能()
        print("[Map] 未配置自动进图，直接开启战斗")
        return true
    end
end

function Automation:doHangActions()
    -- 定时打开目标选择并点第一个怪
    char:开启目标选择怪物界面()
    char:攻击第一个目标怪物()
end

function Automation:doShouts()
    local now = os.time()
    for key, plan in pairs(self.shoutPlan) do
        if plan.enabled and plan.nextAt and now >= plan.nextAt then
            print("[Shout] 执行 " .. key .. " -> " .. tostring(plan.content))
            local ok = char:执行喊话(plan.content)
            -- 喊话失败也照常顺延，避免频繁重试刷屏
            plan.nextAt = now + (plan.interval or 15)
        end
    end
end

function Automation:maybeRestart()
    local opt = userConfig.定时重启
    if not (opt and opt.enable and opt.重启时间) then return false end
    local minutes = tonumber(opt.重启时间) or 0
    if minutes <= 0 then return false end

    local elapsed = os.time() - (self.lastRestartAt or os.time())
    if elapsed >= minutes * 60 then
        print("[Restart] 触发定时重启，间隔(分)=" .. tostring(minutes))
        if packageName and packageName ~= "" then
            utils.close_app(packageName)
            sleep(6000)
            utils.start_app(packageName)
        else
            print("[Restart] 未配置应用包名，跳过关闭/启动，仅执行加载流程")
        end
        char:加载游戏()
        -- 重启后需要重新进图
        self.inMap = false
        self.lastRestartAt = os.time()
        return true
    end
    return false
end

function Automation:runPeriodic()
    local now = os.time()
    -- 禁地：每6小时执行一次
    if now >= (self.nextForbiddenAt or 0) then
        if userConfig.禁地 and userConfig.禁地.enable then
            local r = runTimed("禁地", char.禁地)
            self.inMap = false
            self.nextForbiddenAt = now + 6 * 3600
            print("[Periodic] 禁地完成，下一次: " .. os.date("%H:%M:%S", self.nextForbiddenAt))
        else
            self.nextForbiddenAt = now + 6 * 3600
        end
    end

    -- 世界首领：每15分钟
    if self.worldBossDayKey ~= todayKey() then
        self.worldBossDayKey = todayKey()
        self.worldBossFailCount = 0
    end
    if now >= (self.nextWorldBossAt or 0) then
        if userConfig.世界Boss and userConfig.世界Boss.enable then
            if self.worldBossFailCount < 2 then
                local r = runTimed("世界首领", char.世界首领)
                self.inMap = false
                if r then
                    -- 成功，继续下一次
                    self.nextWorldBossAt = now + 15 * 60
                else
                    self.worldBossFailCount = (self.worldBossFailCount or 0) + 1
                    print("[Periodic] 世界首领返回false，今日累计: " .. tostring(self.worldBossFailCount))
                    if self.worldBossFailCount >= 2 then
                        print("[Periodic] 今日世界首领已失败两次，暂停至明日")
                        -- 下一次设为明天 00:00
                        local t = os.date("*t", now)
                        t.day = t.day + 1; t.hour = 0; t.min = 0; t.sec = 0
                        self.nextWorldBossAt = os.time(t)
                    else
                        self.nextWorldBossAt = now + 15 * 60
                    end
                end
                print("[Periodic] 世界首领下一次: " .. os.date("%H:%M:%S", self.nextWorldBossAt))
            else
                -- 已达失败上限，等到明天
            end
        else
            self.nextWorldBossAt = now + 15 * 60
        end
    end
end

function Automation:runDaily()
    local now = os.time()
    for name, item in pairs(self.dailySchedule or {}) do
        if not item.ran then
            -- 未启用则直接标记为已执行以跳过（避免误触发）
            if item.enabled == false then
                item.ran = true
            else
                -- 只有在“时间窗口内”才执行： [time, time + DAILY_WINDOW_SECONDS)
                if now >= item.time and now < (item.time + DAILY_WINDOW_SECONDS) then
                    local ok = item.run()
                    self.inMap = false
                    item.ran = true
                    print("[Daily] 已执行: " .. name)
                elseif now >= (item.time + DAILY_WINDOW_SECONDS) then
                    -- 超过窗口则本日跳过，并标记为已执行
                    item.ran = true
                    print("[Daily] 已错过窗口，跳过: " .. name)
                end
            end
        end
    end
end

function Automation:runStartupOnce()
    if self.startupDone and self.startupDayKey == todayKey() then return end
    print("[Startup] 执行一次性任务")
    local any = false
    if userConfig.个人首领 and userConfig.个人首领.enable then
        local r = runTimed("个人首领", char.个人首领)
        self.inMap = false
        any = any or r
    end
    if userConfig.入道天途 and userConfig.入道天途.enable then
        local r = runTimed("入道天途", char.入道天途)
        self.inMap = false
        any = any or r
    end
    self.startupDone = true
    self.startupDayKey = todayKey()
end

function Automation:loop()
    print("[Core] 核心循环开始")
    -- 初次启动的任务
    self:runStartupOnce()

    -- 主循环
    while true do
        self:checkDayRollover()

        -- 定时重启（先执行，避免与其他任务冲突）
        local restarted = self:maybeRestart()

        -- 周期性与每日任务
        self:runPeriodic()
        self:runDaily()

        -- 确保进图
        if not self.inMap then
            self:ensureInMap()
        end

        -- 挂机动作 + 喊话
        if self.inMap then
            self:doHangActions()
            self:doShouts()
        end

        -- 小睡一会，避免高频占用
        sleep(1500)
    end
end

return setmetatable({}, { __call = function()
    local o = Automation:new()
    o:loop()
end, __index = Automation })

