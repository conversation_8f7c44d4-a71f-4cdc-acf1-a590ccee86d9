# updateFromUIConfig 函数使用说明

## 功能描述
`updateFromUIConfig` 函数用于将 UI 界面配置数据转换为用户业务配置，更新 `config.userConfig`。

## 函数位置
文件：`脚本/config/config.lua`

## 使用方式
```lua
local config = require("config.config")

-- uiConfig 是从 UI 界面获取的配置数据
local uiConfig = {
    ["喊话1"] = "true",
    ["进入地图"] = "false", 
    ["喊话2内容"] = "测试内容",
    ["个人首领_神器"] = "5",
    ["定时重启"] = "true",
    ["定时重启时间"] = "30",
    -- ... 其他配置项
}

-- 调用函数更新用户配置
config.updateFromUIConfig(uiConfig)
```

## 支持的配置映射

### 1. 定时重启配置
- `定时重启` → `config.userConfig.定时重启.enable`
- `定时重启时间` → `config.userConfig.定时重启.重启时间`

### 2. 地图配置  
- `进入地图` → `config.userConfig.map_enabled.enable`
- `下图模式` → `config.userConfig.map` (根据索引映射到具体地图名)

### 3. 喊话设置
- `喊话1` → `config.userConfig.喊话设置.喊话1.enabled`
- `喊话1内容` → `config.userConfig.喊话设置.喊话1.content`
- `喊话1间隔` → `config.userConfig.喊话设置.喊话1.interval`
- `喊话2内容` → `config.userConfig.喊话设置.喊话2.content`
- `喊话2间隔` → `config.userConfig.喊话设置.喊话2.interval`

### 4. 功能开关
- `武林争霸` → `config.userConfig.武林争霸.enable`
- `世界Boss` → `config.userConfig.世界Boss.enable`
- `禁地` → `config.userConfig.禁地.enable`
- `个人首领` → `config.userConfig.个人首领.enable`
- `入道天途` → `config.userConfig.入道天途.enable`
- `领取宝箱` → `config.userConfig.升级宝箱.enable`
- `散人福利` → `config.userConfig.散人福利.enable`
- `星空秘境` → `config.userConfig.星空秘境.enable`

### 5. 禁地层数配置
- `禁地1层` ~ `禁地5层` → `config.userConfig.禁地.层数` (数组)

### 6. 个人首领分配次数
- `个人首领_元素` → `config.userConfig.个人首领.分配次数.元素`
- `个人首领_装备` → `config.userConfig.个人首领.分配次数.装备`
- `个人首领_龙脉` → `config.userConfig.个人首领.分配次数.龙脉`
- `个人首领_神器` → `config.userConfig.个人首领.分配次数.神器`

## 数据类型转换
函数内置了安全的数据类型转换：
- `toBool()`: 将字符串 "true"/"false" 转换为布尔值
- `toNumber()`: 安全转换为数字，支持默认值
- `toString()`: 安全转换为字符串，支持默认值

## 错误处理
- 如果 `uiConfig` 为空或不是表格类型，函数会输出警告并返回
- 所有配置项都进行了空值检查，只有存在的配置项才会被更新
- 数值转换失败时会使用默认值

## 调试信息
函数会输出详细的更新日志，方便调试：
```
开始更新用户配置...
更新定时重启: true
更新定时重启时间: 30
更新喊话1启用: true
...
用户配置更新完成
```
